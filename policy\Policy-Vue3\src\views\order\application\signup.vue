<template>
  <div class="training-application-container app-container">
    <!-- 使用 TableList 组件 -->
    <TableList v-if="isTableReady" :columns="tableColumns" :data="trainingList" :loading="tableLoading"
      :showIndex="true" :searchColumns="searchableColumns" :showOperation="true" operationLabel="操作"
      operationWidth="300" :fixedOperation="true" ref="tableListRef" @search="handleSearch" @reset="resetSearch"
      :defaultPage="{ pageSize: queryParams.pageSize, currentPage: queryParams.pageNum, total: total }"
      @current-change="handleCurrentChange" @size-change="handleSizeChange" @selection-change="handleSelectionChange">

      <!-- 左侧按钮插槽 -->
      <template #menu-left>
        <el-button type="primary" class="custom-btn" @click="handleRefresh">刷新</el-button>
      </template>

      <!-- 培训类型列插槽 -->
      <template #trainingType="{ row }">
        <el-tag :type="getTypeTagType(row.trainingType)" size="small">
          {{ row.trainingType }}
        </el-tag>
      </template>

      <!-- 培训费用列插槽 -->
      <template #trainingFee="{ row }">
        <span class="price">{{ row.trainingFee ? '￥' + row.trainingFee : '免费' }}</span>
      </template>

      <!-- 培训时间列插槽 -->
      <template #trainingTime="{ row }">
        {{ formatDate(row.startDate) }} 至 {{ formatDate(row.endDate) }}
      </template>

      <!-- 报名人数列插槽 -->
      <template #participants="{ row }">
        {{ row.currentParticipants || 0 }}/{{ row.maxParticipants || 0 }}人
      </template>

      <!-- 申请状态列插槽 -->
      <template #applicationStatus="{ row }">
        <el-tag v-if="row.userApplication && row.userApplication.applicationStatus"
                :type="getApplicationStatusTagType(row.userApplication.applicationStatus)" size="small">
          {{ getApplicationStatusText(row.userApplication.applicationStatus) }}
        </el-tag>
        <span v-else>未申请</span>
      </template>

      <!-- 申请时间列插槽 -->
      <template #applicationTime="{ row }">
        {{ row.userApplication ? formatDate(row.userApplication.applicationTime) : '--' }}
      </template>

      <!-- 操作列插槽 -->
      <template #menu="{ row }">
        <div class="operation-btns">
          <!-- 未申请过或申请被拒绝，可以申请 -->
          <el-button v-if="row.canApply" type="primary" link @click="handleApply(row)"
            :disabled="!canApplyTraining(row)">
            {{ row.userApplication ? '重新申请' : '立即申请' }}
          </el-button>

          <!-- 查看申请信息 -->
          <el-button v-if="row.userApplication" type="success" link
            @click="handleViewApplication(row.userApplication)">
            查看申请
          </el-button>

          <!-- 取消申请 -->
          <el-button v-if="row.userApplication && row.userApplication.applicationStatus === '0'" type="warning" link
            @click="handleCancelApplication(row.userApplication)">
            取消申请
          </el-button>
        </div>
      </template>
    </TableList>
    <div v-else class="loading-placeholder">
      <el-empty description="正在加载表格配置..."></el-empty>
    </div>

    <!-- 申请弹窗 -->
    <el-dialog v-model="applyDialogVisible" :title="`申请 - ${currentTraining?.orderTitle}`" width="800px"
      :close-on-click-modal="false" append-to-body>
      <div class="apply-form">
        <el-form ref="applyFormRef" :model="applyForm" :rules="applyRules" label-width="120px">
          <el-form-item label="申请培训" prop="orderId">
            <el-input v-model="currentTraining.orderTitle" disabled />
          </el-form-item>

          <el-form-item label="申请人姓名" prop="applicantName">
            <el-input v-model="applyForm.applicantName" placeholder="请输入申请人姓名" />
          </el-form-item>

          <el-form-item label="联系电话" prop="applicantPhone">
            <el-input v-model="applyForm.applicantPhone" placeholder="请输入联系电话" />
          </el-form-item>

          <el-form-item label="邮箱地址" prop="applicantEmail">
            <el-input v-model="applyForm.applicantEmail" placeholder="请输入邮箱地址" />
          </el-form-item>

          <el-form-item label="性别" prop="applicantGender">
            <el-select v-model="applyForm.applicantGender" placeholder="请选择性别" style="width: 100%">
              <el-option label="男" value="男"></el-option>
              <el-option label="女" value="女"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="年龄" prop="applicantAge">
            <el-input-number v-model="applyForm.applicantAge" :min="16" :max="100" placeholder="请输入年龄"
              style="width: 100%" />
          </el-form-item>

          <el-form-item label="学历" prop="applicantEducation">
            <el-select v-model="applyForm.applicantEducation" placeholder="请选择学历" style="width: 100%">
              <el-option label="小学" value="小学"></el-option>
              <el-option label="初中" value="初中"></el-option>
              <el-option label="中专" value="中专"></el-option>
              <el-option label="高中" value="高中"></el-option>
              <el-option label="大专" value="大专"></el-option>
              <el-option label="本科" value="本科"></el-option>
              <el-option label="硕士" value="硕士"></el-option>
              <el-option label="博士" value="博士"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="身份证号" prop="applicantIdCard">
            <el-input v-model="applyForm.applicantIdCard" placeholder="请输入身份证号" />
          </el-form-item>

          <el-form-item label="联系地址" prop="applicantAddress">
            <el-input v-model="applyForm.applicantAddress" placeholder="请输入联系地址" />
          </el-form-item>

          <el-form-item label="工作经验" prop="applicantExperience">
            <el-input v-model="applyForm.applicantExperience" type="textarea" :rows="3" placeholder="请简要描述您的工作经验" />
          </el-form-item>

          <el-form-item label="申请备注" prop="applicationNote">
            <el-input v-model="applyForm.applicationNote" type="textarea" :rows="3" placeholder="请输入申请备注（选填）" />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="applyDialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="handleSubmitApply">
            提交申请
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 申请详情弹窗 -->
    <el-dialog v-model="viewDialogVisible" title="申请详情" width="600px" append-to-body>
      <div v-if="currentApplication" class="application-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请人姓名">{{ currentApplication.applicantName }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ currentApplication.applicantPhone }}</el-descriptions-item>
          <el-descriptions-item label="邮箱地址">{{ currentApplication.applicantEmail }}</el-descriptions-item>
          <el-descriptions-item label="性别">{{ currentApplication.applicantGender }}</el-descriptions-item>
          <el-descriptions-item label="年龄">{{ currentApplication.applicantAge }}</el-descriptions-item>
          <el-descriptions-item label="学历">{{ currentApplication.applicantEducation }}</el-descriptions-item>
          <el-descriptions-item label="身份证号">{{ currentApplication.applicantIdCard }}</el-descriptions-item>
          <el-descriptions-item label="联系地址">{{ currentApplication.applicantAddress }}</el-descriptions-item>
          <el-descriptions-item label="申请状态">
            <el-tag :type="getApplicationStatusTagType(currentApplication.applicationStatus)">
              {{ getApplicationStatusText(currentApplication.applicationStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ formatDate(currentApplication.applicationTime) }}</el-descriptions-item>
          <el-descriptions-item label="工作经验" :span="2">{{ currentApplication.applicantExperience || '--'
            }}</el-descriptions-item>
          <el-descriptions-item label="申请备注" :span="2">{{ currentApplication.applicationNote || '--'
            }}</el-descriptions-item>
          <el-descriptions-item v-if="currentApplication.reviewComment" label="审核意见" :span="2">{{
            currentApplication.reviewComment }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance, toRefs } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { listTrainingOrder } from '@/api/training/order'
import { submitTrainingApplication, getMyApplications, cancelMyApplication } from '@/api/training/application'
import TableList from '@/components/TableList/index.vue'

const { proxy } = getCurrentInstance()

// 响应式数据
const loading = ref(false)
const trainingList = ref([])
const applyDialogVisible = ref(false)
const viewDialogVisible = ref(false)
const submitLoading = ref(false)
const currentTraining = ref(null)
const currentApplication = ref(null)
const applyFormRef = ref(null)
const total = ref(0)

// 新的封装组件相关变量
const tableColumns = ref([])
const searchableColumns = ref([]) // 可搜索的字段列表
const tableLoading = ref(false)
const isTableReady = ref(false)
const tableListRef = ref(null)
const searchParams = ref({})

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orderTitle: undefined,
    trainingType: undefined,
    trainingLevel: undefined,
    orderStatus: '1' // 只显示已发布的培训
  }
})

const { queryParams } = toRefs(data)

// 申请表单数据
const applyForm = reactive({
  orderId: null,
  applicantName: '',
  applicantPhone: '',
  applicantEmail: '',
  applicantIdCard: '',
  applicantGender: '',
  applicantAge: null,
  applicantEducation: '',
  applicantExperience: '',
  applicantAddress: '',
  applicationNote: ''
})

// 申请表单验证规则
const applyRules = {
  applicantName: [
    { required: true, message: '请输入申请人姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  applicantPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  applicantEmail: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  applicantIdCard: [
    { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入正确的身份证号', trigger: 'blur' }
  ]
}

// 初始化配置
onMounted(async () => {
  await initializeConfig()
  loadTrainingList()
})

// 初始化配置
const initializeConfig = async () => {
  try {
    // 定义表格列配置
    tableColumns.value = [
      { prop: 'orderTitle', label: '培训标题', minWidth: 200, showOverflowTooltip: true },
      { prop: 'trainingType', label: '培训类型', width: 120, tableSlot: true },
      { prop: 'trainingLevel', label: '培训级别', width: 120 },
      { prop: 'trainingDuration', label: '培训时长', width: 120 },
      { prop: 'trainingFee', label: '培训费用', width: 120, tableSlot: true },
      { prop: 'trainingTime', label: '培训时间', width: 200, tableSlot: true },
      { prop: 'trainingAddress', label: '培训地址', minWidth: 150, showOverflowTooltip: true },
      { prop: 'participants', label: '报名人数', width: 120, tableSlot: true },
      { prop: 'applicationStatus', label: '申请状态', width: 120, tableSlot: true },
      { prop: 'applicationTime', label: '申请时间', width: 150, tableSlot: true }
    ]

    // 定义可搜索的字段
    searchableColumns.value = [
      { prop: 'orderTitle', label: '培训标题', type: 'input' },
      { prop: 'trainingType', label: '培训类型', type: 'input' },
      { prop: 'trainingLevel', label: '培训级别', type: 'input' }
    ]

    isTableReady.value = true
  } catch (error) {
    isTableReady.value = false
    console.error('初始化配置失败:', error)
  }
}

// 方法
const loadTrainingList = async () => {
  tableLoading.value = true
  loading.value = true
  try {
    // 处理日期范围搜索参数
    let params = { ...queryParams.value }
    if (searchParams.value.createTime && Array.isArray(searchParams.value.createTime) && searchParams.value.createTime.length === 2) {
      params = proxy.addDateRange(params, searchParams.value.createTime)
    }

    // 获取所有已发布的培训订单
    const trainingResponse = await listTrainingOrder(params)
    const trainings = trainingResponse.rows || []

    // 获取当前用户的申请记录
    const applicationResponse = await getMyApplications()
    const applications = applicationResponse.data || []

    // 合并培训信息和申请状态
    trainingList.value = trainings.map(training => {
      const userApplication = applications.find(app => app.orderId === training.orderId)
      return {
        ...training,
        userApplication,
        applicationStatus: userApplication?.applicationStatus,
        canApply: !userApplication || userApplication.applicationStatus === '2' || userApplication.applicationStatus === '3'
      }
    })
    total.value = trainingResponse.total || 0
  } catch (error) {
    ElMessage.error('获取培训列表失败')
    console.error(error)
  } finally {
    tableLoading.value = false
    loading.value = false
  }
}

// 处理搜索
const handleSearch = (params) => {
  // 保存搜索参数（包括日期范围）
  searchParams.value = { ...params }

  // 合并搜索参数到queryParams（排除日期范围，因为API需要特殊处理）
  const { createTime, ...otherParams } = params || {}
  Object.assign(queryParams.value, otherParams)
  queryParams.value.pageNum = 1
  loadTrainingList()
}

// 重置搜索
const resetSearch = () => {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    orderTitle: undefined,
    trainingType: undefined,
    trainingLevel: undefined,
    orderStatus: '1' // 只显示已发布的培训
  }
  searchParams.value = {}
  loadTrainingList()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  queryParams.value.pageNum = page
  loadTrainingList()
}

// 处理每页条数变化
const handleSizeChange = (size) => {
  queryParams.value.pageSize = size
  queryParams.value.pageNum = 1
  loadTrainingList()
}

// 处理选择变化
const handleSelectionChange = () => {
  // 可以在这里处理选择逻辑
}

// 刷新数据
const handleRefresh = () => {
  loadTrainingList()
}

const handleApply = (training) => {
  currentTraining.value = training
  applyForm.orderId = training.orderId

  // 如果是重新申请，填充之前的信息
  if (training.userApplication) {
    Object.assign(applyForm, {
      applicantName: training.userApplication.applicantName,
      applicantPhone: training.userApplication.applicantPhone,
      applicantEmail: training.userApplication.applicantEmail,
      applicantIdCard: training.userApplication.applicantIdCard,
      applicantGender: training.userApplication.applicantGender,
      applicantAge: training.userApplication.applicantAge,
      applicantEducation: training.userApplication.applicantEducation,
      applicantExperience: training.userApplication.applicantExperience,
      applicantAddress: training.userApplication.applicantAddress,
      applicationNote: ''
    })
  } else {
    // 重置表单
    Object.assign(applyForm, {
      orderId: training.orderId,
      applicantName: '',
      applicantPhone: '',
      applicantEmail: '',
      applicantIdCard: '',
      applicantGender: '',
      applicantAge: null,
      applicantEducation: '',
      applicantExperience: '',
      applicantAddress: '',
      applicationNote: ''
    })
  }

  applyDialogVisible.value = true
}

const handleSubmitApply = () => {
  if (!applyFormRef.value) return

  applyFormRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true

      try {
        await submitTrainingApplication(applyForm)
        ElMessage.success('申请提交成功，请等待审核')
        applyDialogVisible.value = false

        // 重新加载培训列表
        await loadTrainingList()
      } catch (error) {
        ElMessage.error(error.msg || '申请提交失败，请稍后重试')
      } finally {
        submitLoading.value = false
      }
    }
  })
}

const handleViewApplication = (application) => {
  currentApplication.value = application
  viewDialogVisible.value = true
}

const handleCancelApplication = async (application) => {
  try {
    await ElMessageBox.confirm('确认要取消申请吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await cancelMyApplication(application.applicationId)
    ElMessage.success('取消申请成功')

    // 重新加载培训列表
    await loadTrainingList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(error.msg || '取消申请失败')
    }
  }
}

const canApplyTraining = (training) => {
  // 检查是否可以申请
  const now = new Date()
  const registrationDeadline = training.registrationDeadline ? new Date(training.registrationDeadline) : null

  // 检查报名截止时间
  if (registrationDeadline && now > registrationDeadline) {
    return false
  }

  // 检查报名人数是否已满
  const current = training.currentParticipants || 0
  const max = training.maxParticipants || 0
  if (current >= max && max > 0) {
    return false
  }

  return true
}

const getTypeTagType = (trainingType) => {
  const typeMap = {
    '技能培训': 'primary',
    '管理培训': 'success',
    '安全培训': 'warning',
    '专业培训': 'info'
  }
  return typeMap[trainingType] || 'default'
}

const getApplicationStatusTagType = (status) => {
  if (!status && status !== '0') return 'info'
  const statusMap = {
    '0': 'warning',   // 待审核
    '1': 'success',   // 已通过
    '2': 'danger',    // 已拒绝
    '3': 'info'       // 已取消
  }
  return statusMap[String(status)] || 'info'
}

const getApplicationStatusText = (status) => {
  if (!status && status !== '0') return '未知'
  const statusMap = {
    '0': '待审核',
    '1': '已通过',
    '2': '已拒绝',
    '3': '已取消'
  }
  return statusMap[String(status)] || '未知'
}

const formatDate = (dateTime) => {
  if (!dateTime) return '--'
  return new Date(dateTime).toLocaleDateString('zh-CN')
}
</script>

<style lang="scss" scoped>
.training-application-container {
  .operation-btns {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .price {
    color: #e53e3e;
    font-weight: 700;
  }

  .loading-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
  }
}



</style>
