<template>
  <div class="institution-application-container app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">申请记录</h2>
      <p class="page-description">查看已提交的机构申请记录和审核状态</p>
    </div>

    <!-- 使用 TableList 组件 -->
    <TableList v-if="isTableReady" :columns="tableColumns" :data="trainingList" :loading="tableLoading"
      :showIndex="true" :searchColumns="searchableColumns" :showOperation="true" operationLabel="操作"
      operationWidth="300" :fixedOperation="true" ref="tableListRef" @search="handleSearch" @reset="resetSearch"
      :defaultPage="{ pageSize: queryParams.pageSize, currentPage: queryParams.pageNum, total: total }"
      @current-change="handleCurrentChange" @size-change="handleSizeChange" @selection-change="handleSelectionChange">

      <!-- 左侧按钮插槽 -->
      <template #menu-left>
        <el-button type="primary" class="custom-btn" @click="handleRefresh">刷新</el-button>
      </template>

      <!-- 培训类型列插槽 -->
      <template #trainingType="{ row }">
        <el-tag :type="getTypeTagType(row.trainingType)" size="small">
          {{ row.trainingType }}
        </el-tag>
      </template>

      <!-- 培训费用列插槽 -->
      <template #trainingFee="{ row }">
        <span class="price">{{ row.trainingFee ? '￥' + row.trainingFee : '面议' }}</span>
      </template>

      <!-- 培训时间列插槽 -->
      <template #trainingTime="{ row }">
        {{ formatDate(row.startDate) }} 至 {{ formatDate(row.endDate) }}
      </template>

      <!-- 申请截止时间列插槽 -->
      <template #registrationDeadline="{ row }">
        {{ formatDate(row.registrationDeadline) }}
      </template>

      <!-- 申请状态列插槽 -->
      <template #applicationStatus="{ row }">
        <el-tag v-if="row.institutionApplication && row.institutionApplication.applicationStatus"
                :type="getApplicationStatusTagType(row.institutionApplication.applicationStatus)" size="small">
          {{ getApplicationStatusText(row.institutionApplication.applicationStatus) }}
        </el-tag>
        <span v-else>未申请</span>
      </template>

      <!-- 申请时间列插槽 -->
      <template #applicationTime="{ row }">
        {{ row.institutionApplication ? formatDate(row.institutionApplication.applicationTime) : '--' }}
      </template>

      <!-- 操作列插槽 -->
      <template #menu="{ row }">
        <div class="operation-btns">
          <!-- 查看申请信息 -->
          <el-button type="success" link @click="handleViewApplication(row.institutionApplication)">
            查看详情
          </el-button>

          <!-- 取消申请 -->
          <el-button v-if="row.institutionApplication.applicationStatus === '0'" type="warning" link
            @click="handleCancelApplication(row.institutionApplication)">
            取消申请
          </el-button>
        </div>
      </template>
    </TableList>
    <div v-else class="loading-placeholder">
      <el-empty description="正在加载表格配置..."></el-empty>
    </div>



    <!-- 申请详情弹窗 -->
    <el-dialog v-model="viewDialogVisible" title="申请详情" width="1000px" append-to-body destroy-on-close>
      <div v-if="currentApplication" class="application-detail">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h4 class="section-title">基本信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="机构名称">{{ currentApplication.institutionName }}</el-descriptions-item>
            <el-descriptions-item label="机构代码">{{ currentApplication.institutionCode }}</el-descriptions-item>
            <el-descriptions-item label="法定代表人">{{ currentApplication.legalPerson }}</el-descriptions-item>
            <el-descriptions-item label="机构类型">{{ currentApplication.institutionType }}</el-descriptions-item>
            <el-descriptions-item label="联系人">{{ currentApplication.contactPerson }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{ currentApplication.contactPhone }}</el-descriptions-item>
            <el-descriptions-item label="联系邮箱">{{ currentApplication.contactEmail }}</el-descriptions-item>
            <el-descriptions-item label="成立时间">{{ formatDate(currentApplication.establishedDate)
              }}</el-descriptions-item>
            <el-descriptions-item label="注册资本">{{ currentApplication.registeredCapital }}万元</el-descriptions-item>
            <el-descriptions-item label="申请状态">
              <el-tag :type="getApplicationStatusTagType(currentApplication.applicationStatus)">
                {{ getApplicationStatusText(currentApplication.applicationStatus) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="申请时间">{{ formatDate(currentApplication.applicationTime)
              }}</el-descriptions-item>
            <el-descriptions-item label="机构地址" :span="2">{{ currentApplication.institutionAddress
              }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 培训能力信息 -->
        <div class="detail-section">
          <h4 class="section-title">培训能力</h4>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="经营范围">{{ currentApplication.businessScope || '--' }}</el-descriptions-item>
            <el-descriptions-item label="培训经验">{{ currentApplication.trainingExperience || '--'
              }}</el-descriptions-item>
            <el-descriptions-item label="培训能力">{{ currentApplication.trainingCapacity || '--' }}</el-descriptions-item>
            <el-descriptions-item label="培训计划">{{ currentApplication.trainingPlan || '--' }}</el-descriptions-item>
            <el-descriptions-item label="师资信息">{{ currentApplication.teacherInfo || '--' }}</el-descriptions-item>
            <el-descriptions-item label="设施设备">{{ currentApplication.facilityInfo || '--' }}</el-descriptions-item>
            <el-descriptions-item v-if="currentApplication.applicationNote" label="申请备注">{{
              currentApplication.applicationNote }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 申请材料 -->
        <div class="detail-section">
          <h4 class="section-title">申请材料</h4>
          <div class="uploaded-materials">
            <div class="material-item" v-for="(material, index) in viewMaterials" :key="index">
              <div class="material-header">
                <div class="material-info">
                  <div class="material-name">
                    <el-icon class="material-icon">
                      <Document />
                    </el-icon>
                    <span>{{ material.name }}</span>
                  </div>
                  <div class="material-status">
                    <el-tag v-if="material.files && material.files.length > 0" type="success" size="small">
                      已上传 {{ material.files.length }} 个文件
                    </el-tag>
                    <el-tag v-else type="info" size="small">未上传</el-tag>
                  </div>
                </div>
              </div>

              <div class="material-files" v-if="material.files && material.files.length > 0">
                <div class="file-grid">
                  <div class="file-card" v-for="(file, fileIndex) in material.files" :key="fileIndex">
                    <FileView
                      :file="{ filePath: file.url || file.filePath, sourceFileName: file.name || file.fileName }" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 审核信息 -->
        <div class="detail-section" v-if="currentApplication.applicationStatus !== '0'">
          <h4 class="section-title">审核信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="审核时间">{{ formatDate(currentApplication.reviewTime) }}</el-descriptions-item>
            <el-descriptions-item label="审核人">{{ currentApplication.reviewer || '--' }}</el-descriptions-item>
            <el-descriptions-item v-if="currentApplication.reviewComment" label="审核意见" :span="2">{{
              currentApplication.reviewComment }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance, toRefs } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
import { listTrainingOrder } from '@/api/training/order'
import {
  getMyInstitutionApplications,
  cancelMyInstitutionApplication
} from '@/api/training/institutionApplication'
import FileView from '@/components/FileView/index.vue'
import TableList from '@/components/TableList/index.vue'

const { proxy } = getCurrentInstance()

// 响应式数据
const loading = ref(false)
const trainingList = ref([])
const viewDialogVisible = ref(false)
const currentApplication = ref(null)
const total = ref(0)

// 新的封装组件相关变量
const tableColumns = ref([])
const searchableColumns = ref([]) // 可搜索的字段列表
const tableLoading = ref(false)
const isTableReady = ref(false)
const tableListRef = ref(null)
const searchParams = ref({})

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orderTitle: undefined,
    trainingType: undefined,
    trainingLevel: undefined,
    orderStatus: '1' // 只显示已发布的培训
  }
})

const { queryParams } = toRefs(data)



// 查看材料列表（用于详情弹窗）
const viewMaterials = ref([
  {
    name: '机构营业执照或组织机构代码证',
    files: [],
    field: 'qualificationFiles'
  },
  {
    name: '培训计划详细方案',
    files: [],
    field: 'trainingPlanFile'
  },
  {
    name: '师资队伍资质证明材料',
    files: [],
    field: 'teacherCertFiles'
  },
  {
    name: '培训场地及设施设备证明',
    files: [],
    field: 'facilityFiles'
  },
  {
    name: '其他相关资质证明材料',
    files: [],
    field: 'otherFiles'
  }
])

// 初始化配置
onMounted(async () => {
  await initializeConfig()
  loadTrainingList()
})

// 初始化配置
const initializeConfig = async () => {
  try {
    // 定义表格列配置
    tableColumns.value = [
      { prop: 'orderTitle', label: '培训标题', minWidth: 200, showOverflowTooltip: true },
      { prop: 'trainingType', label: '培训类型', width: 120, tableSlot: true },
      { prop: 'trainingLevel', label: '培训级别', width: 120 },
      { prop: 'trainingDuration', label: '培训时长', width: 120 },
      { prop: 'trainingFee', label: '培训费用', width: 120, tableSlot: true },
      { prop: 'trainingTime', label: '培训时间', width: 200, tableSlot: true },
      { prop: 'trainingAddress', label: '培训地址', minWidth: 150, showOverflowTooltip: true },
      { prop: 'registrationDeadline', label: '申请截止', width: 150, tableSlot: true },
      { prop: 'applicationStatus', label: '申请状态', width: 120, tableSlot: true },
      { prop: 'applicationTime', label: '申请时间', width: 150, tableSlot: true }
    ]

    // 定义可搜索的字段
    searchableColumns.value = [
      { prop: 'orderTitle', label: '培训标题', type: 'input' },
      { prop: 'trainingType', label: '培训类型', type: 'input' },
      { prop: 'trainingLevel', label: '培训级别', type: 'input' }
    ]

    isTableReady.value = true
  } catch (error) {
    isTableReady.value = false
    console.error('初始化配置失败:', error)
  }
}

// 方法
const loadTrainingList = async () => {
  tableLoading.value = true
  loading.value = true
  try {
    // 获取当前用户的机构申请记录
    const applicationResponse = await getMyInstitutionApplications()
    const applications = applicationResponse.data || []

    if (applications.length === 0) {
      trainingList.value = []
      total.value = 0
      return
    }

    // 处理日期范围搜索参数
    let params = { ...queryParams.value }
    if (searchParams.value.createTime && Array.isArray(searchParams.value.createTime) && searchParams.value.createTime.length === 2) {
      params = proxy.addDateRange(params, searchParams.value.createTime)
    }

    // 获取培训订单信息
    const trainingResponse = await listTrainingOrder(params)
    const trainings = trainingResponse.rows || []

    // 只显示已申请的培训项目（申请记录页面只显示已申请的）
    trainingList.value = applications
      .map(application => {
        const training = trainings.find(t => t.orderId === application.orderId)
        if (!training) return null

        return {
          ...training,
          institutionApplication: application,
          applicationStatus: application.applicationStatus,
          canApply: application.applicationStatus === '2' || application.applicationStatus === '3' // 只有被拒绝或已取消的可以重新申请
        }
      })
      .filter(item => item !== null) // 过滤掉找不到培训信息的记录
      .filter(item => {
        // 根据搜索条件过滤
        if (params.orderTitle && !item.orderTitle?.includes(params.orderTitle)) return false
        if (params.trainingType && !item.trainingType?.includes(params.trainingType)) return false
        if (params.trainingLevel && !item.trainingLevel?.includes(params.trainingLevel)) return false
        return true
      })

    total.value = trainingList.value.length
  } catch (error) {
    ElMessage.error('获取申请记录失败')
    console.error(error)
  } finally {
    tableLoading.value = false
    loading.value = false
  }
}

// 处理搜索
const handleSearch = (params) => {
  // 保存搜索参数（包括日期范围）
  searchParams.value = { ...params }

  // 合并搜索参数到queryParams（排除日期范围，因为API需要特殊处理）
  const { createTime, ...otherParams } = params || {}
  Object.assign(queryParams.value, otherParams)
  queryParams.value.pageNum = 1
  loadTrainingList()
}

// 重置搜索
const resetSearch = () => {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    orderTitle: undefined,
    trainingType: undefined,
    trainingLevel: undefined,
    orderStatus: '1' // 只显示已发布的培训
  }
  searchParams.value = {}
  loadTrainingList()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  queryParams.value.pageNum = page
  loadTrainingList()
}

// 处理每页条数变化
const handleSizeChange = (size) => {
  queryParams.value.pageSize = size
  queryParams.value.pageNum = 1
  loadTrainingList()
}

// 处理选择变化
const handleSelectionChange = () => {
  // 可以在这里处理选择逻辑
}

// 刷新数据
const handleRefresh = () => {
  loadTrainingList()
}





const handleViewApplication = (application) => {
  currentApplication.value = application

  // 调试：打印申请数据
  console.log('申请数据:', application)

  // 处理文件数据
  viewMaterials.value.forEach(material => {
    const fileData = application[material.field]
    console.log(`${material.name} 文件数据:`, fileData)

    if (fileData) {
      try {
        let parsedFiles = JSON.parse(fileData) || []
        // 确保文件对象包含所有必要字段
        material.files = parsedFiles.map(file => ({
          name: file.name || file.fileName || file.sourceFileName,
          fileName: file.fileName || file.name || file.sourceFileName,
          sourceFileName: file.sourceFileName || file.name || file.fileName,
          url: file.url || file.filePath,
          filePath: file.filePath || file.url
        }))
        console.log(`${material.name} 解析后文件:`, material.files)
      } catch (error) {
        console.error(`${material.name} JSON解析失败:`, error)
        material.files = []
      }
    } else {
      material.files = []
    }
  })

  viewDialogVisible.value = true
}

const handleCancelApplication = async (application) => {
  try {
    await ElMessageBox.confirm('确认要取消申请吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await cancelMyInstitutionApplication(application.applicationId)
    ElMessage.success('取消申请成功')

    // 重新加载培训列表
    await loadTrainingList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(error.msg || '取消申请失败')
    }
  }
}



const getTypeTagType = (trainingType) => {
  const typeMap = {
    '技能培训': 'primary',
    '管理培训': 'success',
    '安全培训': 'warning',
    '专业培训': 'info'
  }
  return typeMap[trainingType] || 'default'
}

const getApplicationStatusTagType = (status) => {
  const statusMap = {
    '0': 'warning',   // 待审核
    '1': 'success',   // 已通过
    '2': 'danger',    // 已拒绝
    '3': 'info'       // 已取消
  }
  return statusMap[status] || 'info'
}

const getApplicationStatusText = (status) => {
  const statusMap = {
    '0': '待审核',
    '1': '已通过',
    '2': '已拒绝',
    '3': '已取消'
  }
  return statusMap[status] || '未知'
}



const formatDate = (dateTime) => {
  if (!dateTime) return '--'
  return new Date(dateTime).toLocaleDateString('zh-CN')
}


</script>

<style lang="scss" scoped>
.institution-application-container {
  .page-header {
    margin-bottom: 20px;
    padding: 20px 0;
    border-bottom: 1px solid #e2e8f0;

    .page-title {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #2d3748;
    }

    .page-description {
      margin: 0;
      font-size: 14px;
      color: #718096;
    }
  }

  .operation-btns {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .price {
    color: #e53e3e;
    font-weight: 700;
  }

  .loading-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
  }
}





// 查看详情中的材料展示样式
.uploaded-materials {
  .material-item {
    margin-bottom: 25px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    border: 1px solid rgba(226, 232, 240, 0.8);

    .material-header {
      margin-bottom: 15px;

      .material-info {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .material-name {
          display: flex;
          align-items: center;
          font-weight: 600;
          color: #2d3748;
          font-size: 15px;

          .material-icon {
            margin-right: 8px;
            color: #667eea;
            font-size: 18px;
          }
        }
      }
    }

    .material-files {
      .file-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        margin-top: 12px;

        .file-card {
          background: rgba(248, 250, 252, 0.9);
          border: 1px solid rgba(226, 232, 240, 0.8);
          border-radius: 12px;
          padding: 12px;
        }
      }
    }
  }
}

// 详情弹窗样式
.application-detail {
  .detail-section {
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(248, 250, 252, 0.5);
    border-radius: 8px;
    border: 1px solid rgba(226, 232, 240, 0.8);

    .section-title {
      margin: 0 0 20px 0;
      font-size: 16px;
      font-weight: 600;
      color: #2d3748;
      padding-bottom: 10px;
      border-bottom: 2px solid #667eea;
    }
  }
}


</style>
