<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>机构申请功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-title {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .test-case {
            margin: 15px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #28a745;
        }
        .expected {
            color: #28a745;
            font-weight: bold;
        }
        .code-block {
            background-color: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin: 2px;
        }
        .status-pending { background-color: #ffc107; color: #000; }
        .status-approved { background-color: #28a745; color: #fff; }
        .status-rejected { background-color: #dc3545; color: #fff; }
        .status-cancelled { background-color: #6c757d; color: #fff; }
    </style>
</head>
<body>
    <h1>机构申请功能拆分测试</h1>
    
    <div class="test-section">
        <h2 class="test-title">1. 申请页面 (institution-apply.vue) 测试</h2>
        
        <div class="test-case">
            <h3>测试用例 1.1: 数据过滤逻辑</h3>
            <p><strong>测试目标：</strong> 验证申请页面只显示未申请的培训项目</p>
            <p><strong>测试数据：</strong></p>
            <div class="code-block">
培训项目A - 无申请记录 → <span class="expected">应显示</span><br>
培训项目B - 申请状态: <span class="status-pending">待审核</span> → 不应显示<br>
培训项目C - 申请状态: <span class="status-approved">已通过</span> → 不应显示<br>
培训项目D - 申请状态: <span class="status-rejected">已拒绝</span> → <span class="expected">应显示</span><br>
培训项目E - 申请状态: <span class="status-cancelled">已取消</span> → <span class="expected">应显示</span>
            </div>
        </div>
        
        <div class="test-case">
            <h3>测试用例 1.2: 操作按钮显示</h3>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>未申请的项目：显示 "立即申请" 按钮</li>
                <li>被拒绝/已取消的项目：显示 "重新申请" 按钮</li>
                <li>有申请记录的项目：显示 "查看申请" 按钮</li>
                <li>不显示 "取消申请" 按钮</li>
            </ul>
        </div>
        
        <div class="test-case">
            <h3>测试用例 1.3: 页面标题</h3>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>页面标题：机构申请</li>
                <li>页面描述：查看可申请的培训项目，提交机构申请</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2 class="test-title">2. 申请记录页面 (institution-record.vue) 测试</h2>
        
        <div class="test-case">
            <h3>测试用例 2.1: 数据过滤逻辑</h3>
            <p><strong>测试目标：</strong> 验证申请记录页面只显示已申请的培训项目</p>
            <p><strong>测试数据：</strong></p>
            <div class="code-block">
培训项目A - 无申请记录 → 不应显示<br>
培训项目B - 申请状态: <span class="status-pending">待审核</span> → <span class="expected">应显示</span><br>
培训项目C - 申请状态: <span class="status-approved">已通过</span> → <span class="expected">应显示</span><br>
培训项目D - 申请状态: <span class="status-rejected">已拒绝</span> → <span class="expected">应显示</span><br>
培训项目E - 申请状态: <span class="status-cancelled">已取消</span> → <span class="expected">应显示</span>
            </div>
        </div>
        
        <div class="test-case">
            <h3>测试用例 2.2: 操作按钮显示</h3>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>所有记录：显示 "查看详情" 按钮</li>
                <li>待审核状态：显示 "取消申请" 按钮</li>
                <li>其他状态：不显示 "取消申请" 按钮</li>
                <li>不显示申请相关按钮</li>
            </ul>
        </div>
        
        <div class="test-case">
            <h3>测试用例 2.3: 页面标题</h3>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>页面标题：申请记录</li>
                <li>页面描述：查看已提交的机构申请记录和审核状态</li>
            </ul>
        </div>
        
        <div class="test-case">
            <h3>测试用例 2.4: 功能移除验证</h3>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>不应有申请弹窗</li>
                <li>不应有申请表单相关代码</li>
                <li>不应有文件上传功能</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2 class="test-title">3. 功能完整性测试</h2>
        
        <div class="test-case">
            <h3>测试用例 3.1: 申请流程</h3>
            <p><strong>测试步骤：</strong></p>
            <ol>
                <li>在申请页面查看可申请的培训项目</li>
                <li>点击 "立即申请" 按钮</li>
                <li>填写申请表单并提交</li>
                <li>切换到申请记录页面</li>
                <li>验证新申请出现在记录列表中</li>
                <li>验证申请页面不再显示该培训项目</li>
            </ol>
        </div>
        
        <div class="test-case">
            <h3>测试用例 3.2: 取消申请流程</h3>
            <p><strong>测试步骤：</strong></p>
            <ol>
                <li>在申请记录页面找到待审核的申请</li>
                <li>点击 "取消申请" 按钮</li>
                <li>确认取消操作</li>
                <li>验证申请状态变为已取消</li>
                <li>切换到申请页面</li>
                <li>验证该培训项目重新出现在可申请列表中</li>
            </ol>
        </div>
    </div>
    
    <div class="test-section">
        <h2 class="test-title">4. 代码质量检查</h2>
        
        <div class="test-case">
            <h3>检查项目</h3>
            <ul>
                <li>✅ 无语法错误</li>
                <li>✅ 移除未使用的导入</li>
                <li>✅ 移除未使用的变量和函数</li>
                <li>✅ 代码结构清晰</li>
                <li>✅ 功能分离明确</li>
            </ul>
        </div>
    </div>
    
    <script>
        console.log('机构申请功能拆分测试页面已加载');
        console.log('请按照测试用例进行手动测试验证');
    </script>
</body>
</html>
